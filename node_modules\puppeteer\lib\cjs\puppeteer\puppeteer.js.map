{"version": 3, "file": "puppeteer.js", "sourceRoot": "", "sources": ["../../../src/puppeteer.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;;;;;;;;;;;;;;;AAEH,iDAA+B;AAE/B,mDAA6C;AAE7C,+DAAuD;AAEvD,MAAM,aAAa,GAAG,IAAA,sCAAgB,GAAE,CAAC;AAEzC;;GAEG;AACH,uCAAuC;AACvC,MAAM,SAAS,GAAG,IAAI,8BAAa,CAAC;IAClC,eAAe,EAAE,KAAK;IACtB,aAAa;CACd,CAAC,CAAC;AAEW,eAAO,GACnB,SAAS,UADY,mBAAW,GAChC,SAAS,cADyB,sBAAc,GAChD,SAAS,iBADyC,cAAM,GACxD,SAAS,SADiD,iBAAS,GACnE,SAAS,WAAC;AAEZ,kBAAe,SAAS,CAAC"}