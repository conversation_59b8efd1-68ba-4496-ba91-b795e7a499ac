{"version": 3, "file": "puppeteer.js", "sourceRoot": "", "sources": ["../../../src/puppeteer.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,cAAc,gBAAgB,CAAC;AAE/B,OAAO,EAAC,aAAa,EAAC,MAAM,gBAAgB,CAAC;AAE7C,OAAO,EAAC,gBAAgB,EAAC,MAAM,uBAAuB,CAAC;AAEvD,MAAM,aAAa,GAAG,gBAAgB,EAAE,CAAC;AAEzC;;GAEG;AACH,uCAAuC;AACvC,MAAM,SAAS,GAAG,IAAI,aAAa,CAAC;IAClC,eAAe,EAAE,KAAK;IACtB,aAAa;CACd,CAAC,CAAC;AAEH,MAAM,CAAC,MAAM,EAAC,OAAO,EAAE,WAAW,EAAE,cAAc,EAAE,MAAM,EAAE,SAAS,EAAC,GACpE,SAAS,CAAC;AAEZ,eAAe,SAAS,CAAC"}