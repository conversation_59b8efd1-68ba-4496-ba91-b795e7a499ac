/**
 * @license
 * Copyright 2017 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
export * from 'puppeteer-core';
import { PuppeteerNode } from 'puppeteer-core';
/**
 * @public
 */
declare const puppeteer: PuppeteerNode;
export declare const connect: (options: import("puppeteer-core").ConnectOptions) => Promise<import("puppeteer-core").Browser>, defaultArgs: (options?: import("puppeteer-core").LaunchOptions) => string[], executablePath: {
    (channel: import("puppeteer-core").ChromeReleaseChannel): string;
    (options: import("puppeteer-core").LaunchOptions): string;
    (): string;
}, launch: (options?: import("puppeteer-core").LaunchOptions) => Promise<import("puppeteer-core").Browser>, trimCache: () => Promise<void>;
export default puppeteer;
//# sourceMappingURL=puppeteer.d.ts.map