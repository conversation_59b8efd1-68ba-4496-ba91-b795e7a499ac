const puppeteer = require("puppeteer");
const readline = require("readline");

function askQuestion(query) {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });
  return new Promise(resolve => rl.question(query, ans => {
    rl.close();
    resolve(ans);
  }))
}

(async () => {
  
  const browser = await puppeteer.launch({
    headless: false, // precisa ser "false" para permitir clicar nos botões
    args: ["--kiosk-printing"], // impressão silenciosa
  });

  const page = await browser.newPage();

  await page.goto("https://doity.com.br/admin/users/login");

  await page.type("#UserUsername", "<EMAIL>");
  await page.type("#UserPassword", "Davicomunic22#");
  await page.click('input[value="Entrar"]');
  await page.waitForNavigation();

  let currentPage = await askQuestion("Digite o número da página para começar: ");
  currentPage = parseInt(currentPage);
  if (isNaN(currentPage) || currentPage < 1) currentPage = 1;

  let hasNext = true;

  while (hasNext) {

    // Navegar para a página atual
    await page.goto(`https://doity.com.br/admin/credenciamento/index/263089/page:${currentPage}`, { waitUntil: "networkidle0" });

    // ---- REMOVER CHAT FLUTUANTE ----
    try {
      await page.evaluate(() => {
        const chat = document.querySelector(".live-chat-widget, #chat-widget");
        if (chat) chat.remove();
      });
    } catch (err) {
      console.log("Chat não encontrado ou já removido.");
    }

    // ---- LOOP DE IMPRESSÃO ----
    const printButtons = await page.$$("#bt-imprimir-etiqueta");
    for (let i = 0; i < printButtons.length; i++) {
      const currentButtons = await page.$$("#bt-imprimir-etiqueta");
      const button = currentButtons[i];
      if (!button) continue;

      console.log(`Página ${currentPage} - Imprimindo inscrito ${i + 1}`);
      await page.evaluate((btn) => btn.click(), button);
      await new Promise(resolve => setTimeout(resolve, 2000));
    }

    // ---- PERGUNTAR AO USUÁRIO SE QUER AVANÇAR ----
    const answer = await askQuestion("Pressione ENTER para ir para a próxima página ou digite 'sair' para encerrar: ");
    if (answer.toLowerCase() === "sair") {
      hasNext = false;
    } else {
      currentPage++; // incrementar página manualmente
    }
  }

  await browser.close();
})();
