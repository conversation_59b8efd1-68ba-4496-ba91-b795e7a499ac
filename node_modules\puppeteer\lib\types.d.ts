import { <PERSON><PERSON><PERSON> } from 'puppeteer-core';
import { ChromeReleaseChannel } from 'puppeteer-core';
import { ConnectOptions } from 'puppeteer-core';
import { LaunchOptions } from 'puppeteer-core';
import { PuppeteerNode } from 'puppeteer-core';

export declare const connect: (options: ConnectOptions) => Promise<Browser>;

export declare const defaultArgs: (options?: LaunchOptions) => string[];

export declare const executablePath: {
    (channel: ChromeReleaseChannel): string;
    (options: LaunchOptions): string;
    (): string;
};

export declare const launch: (options?: LaunchOptions) => Promise<Browser>;

/**
 * @public
 */
declare const puppeteer: PuppeteerNode;
export default puppeteer;

export declare const trimCache: () => Promise<void>;


export * from "puppeteer-core";

export { }
